❌ 路由注册失败: View function mapping is overwriting an existing endpoint function: batch_update_learning_type
❌ 路由注册失败: View function mapping is overwriting an existing endpoint function: batch_update_learning_type
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Lei_MBP/repo/app_dev/word_learning_app_v5_prod/app.py", line 324, in <module>
    app = create_app()
  File "/Users/<USER>/Documents/Lei_MBP/repo/app_dev/word_learning_app_v5_prod/app.py", line 60, in create_app
    register_all_routes(app)
    ~~~~~~~~~~~~~~~~~~~^^^^^
  File "/Users/<USER>/Documents/Lei_MBP/repo/app_dev/word_learning_app_v5_prod/src/routes/registry.py", line 30, in register_all_routes
    register_admin_routes(app)
    ~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "/Users/<USER>/Documents/Lei_MBP/repo/app_dev/word_learning_app_v5_prod/src/routes/admin_routes.py", line 583, in register_admin_routes
    @app.route('/admin/batch_update_learning_type', methods=['POST'])
     ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Lei_MBP/repo/app_dev/word_learning_app_v5_prod/venv/lib/python3.13/site-packages/flask/sansio/scaffold.py", line 362, in decorator
    self.add_url_rule(rule, endpoint, f, **options)
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Lei_MBP/repo/app_dev/word_learning_app_v5_prod/venv/lib/python3.13/site-packages/flask/sansio/scaffold.py", line 47, in wrapper_func
    return f(self, *args, **kwargs)
  File "/Users/<USER>/Documents/Lei_MBP/repo/app_dev/word_learning_app_v5_prod/venv/lib/python3.13/site-packages/flask/sansio/app.py", line 657, in add_url_rule
    raise AssertionError(
    ...<2 lines>...
    )
AssertionError: View function mapping is overwriting an existing endpoint function: batch_update_learning_type
