#!/usr/bin/env python3
"""
调试脚本：列出所有已注册的Flask路由
"""

import requests
import sys

def list_app_routes():
    """通过应用内部API获取路由信息"""
    try:
        # 首先检查应用是否运行
        response = requests.get("http://localhost:5005/", timeout=5)
        print("✅ 应用正在运行")
        
        # 查找pattern相关的路由
        pattern_routes = [
            "/api/word_pattern_suggestions/<int:word_id>",
            "/api/pattern_learning_suggestions", 
            "/api/pattern_interaction"
        ]
        
        print("\n🔍 测试Pattern API路由:")
        for route in pattern_routes:
            test_url = route.replace('<int:word_id>', '415')  # 用真实ID替换参数
            full_url = f"http://localhost:5005{test_url}"
            
            try:
                # 使用HEAD请求测试路由是否存在
                response = requests.head(full_url, timeout=5)
                if response.status_code == 404:
                    print(f"❌ {route} - 路由不存在 (404)")
                elif response.status_code == 401:
                    print(f"✅ {route} - 路由存在但需要认证 (401)")
                elif response.status_code == 405:
                    print(f"✅ {route} - 路由存在但方法不允许 (405)")
                else:
                    print(f"✅ {route} - 路由存在 ({response.status_code})")
            except Exception as e:
                print(f"❌ {route} - 测试失败: {e}")
        
    except requests.exceptions.ConnectionError:
        print("❌ 应用未运行，请先启动 python app.py")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    list_app_routes()