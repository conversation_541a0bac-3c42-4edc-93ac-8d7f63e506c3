<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recognition模式测试</title>
    <link rel="stylesheet" href="/static/css/learning-page.css">
    <style>
        body {
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-info {
            background: #f0f8ff;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 12px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Recognition模式多选题测试</h1>
        
        <div class="debug-info">
            <strong>测试说明：</strong>点击下方按钮加载babysitter的多选题，检查选项是否正确显示
        </div>
        
        <button onclick="testBabysitterQuestion()" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">测试babysitter多选题</button>
        
        <div id="hint">点击上方按钮开始测试</div>
        
        <!-- Recognition模式界面 -->
        <div id="recognition-mode" style="display: none;">
            <div id="recognition-container">
                <!-- 问题显示区域 -->
                <div id="recognition-question">
                    <div id="question-text">请选择正确答案：</div>
                </div>
                
                <!-- 选择题选项区域 -->
                <div id="recognition-options">
                    <div class="option-button" data-option="A" onclick="selectRecognitionOption('A')">
                        <span class="option-label">A</span>
                        <span class="option-text" id="option-A-text"></span>
                    </div>
                    <div class="option-button" data-option="B" onclick="selectRecognitionOption('B')">
                        <span class="option-label">B</span>
                        <span class="option-text" id="option-B-text"></span>
                    </div>
                    <div class="option-button" data-option="C" onclick="selectRecognitionOption('C')">
                        <span class="option-label">C</span>
                        <span class="option-text" id="option-C-text"></span>
                    </div>
                    <div class="option-button" data-option="D" onclick="selectRecognitionOption('D')">
                        <span class="option-label">D</span>
                        <span class="option-text" id="option-D-text"></span>
                    </div>
                </div>
                
                <!-- 确认按钮 -->
                <div id="recognition-submit">
                    <button id="recognition-confirm-btn" onclick="confirmTest()" disabled>确认答案</button>
                </div>
                
                <!-- 反馈区域 -->
                <div id="recognition-feedback" style="display: none;">
                    <div id="recognition-result"></div>
                    <div id="recognition-explanation"></div>
                </div>
            </div>
        </div>
        
        <div id="debug-log" class="debug-info"></div>
    </div>

    <script>
        // Recognition模式的全局变量
        let currentRecognitionData = null;
        let selectedRecognitionOptions = [];
        
        function log(message) {
            console.log(message);
            const debugLog = document.getElementById('debug-log');
            debugLog.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
        }
        
        async function testBabysitterQuestion() {
            log('🚀 开始测试babysitter多选题...');
            
            try {
                const response = await fetch('/api/recognition/question', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        word_id: 986,
                        question_type: 'en_to_cn'
                    })
                });
                
                const data = await response.json();
                log('📨 API响应: ' + JSON.stringify(data, null, 2));
                
                if (data.success) {
                    currentRecognitionData = data.data;
                    document.getElementById('recognition-mode').style.display = 'block';
                    displayRecognitionQuestion();
                } else {
                    log('❌ API返回失败: ' + data.message);
                }
            } catch (error) {
                log('❌ 请求异常: ' + error.message);
            }
        }
        
        function displayRecognitionQuestion() {
            if (!currentRecognitionData) return;
            
            const { question, question_type, options, correct_answers, is_multiple_choice, total_meanings } = currentRecognitionData;
            
            log('🔍 显示问题数据: ' + JSON.stringify(currentRecognitionData, null, 2));
            
            // 设置问题文本
            let questionText;
            if (is_multiple_choice) {
                questionText = question_type === 'cn_to_en' 
                    ? `请选择"${question}"的英文：`
                    : `请选择"${question}"的所有中文意思（多选，共${total_meanings}个含义）：`;
            } else {
                questionText = question_type === 'cn_to_en' 
                    ? `请选择"${question}"的英文：`
                    : `请选择"${question}"的中文意思：`;
            }
            
            document.getElementById('question-text').textContent = questionText;
            
            // 设置提示
            const hintElement = document.getElementById('hint');
            if (is_multiple_choice) {
                if (question_type === 'en_to_cn') {
                    hintElement.innerHTML = `<div style="color: #ff6b35; font-weight: bold;">📌 多选题：该单词有${total_meanings}个含义，请全部选中</div>`;
                } else {
                    hintElement.textContent = '请从下方选项中选择正确的英文';
                }
            } else {
                hintElement.textContent = '请从下方选项中选择正确答案';
            }
            
            // 设置选项
            log('🔍 开始设置选项，共' + options.length + '个选项');
            options.forEach((option, index) => {
                const optionKey = String.fromCharCode(65 + index); // A, B, C, D
                const optionElement = document.getElementById(`option-${optionKey}-text`);
                const optionButton = document.querySelector(`[data-option="${optionKey}"]`);
                
                log(`🔍 设置选项 ${optionKey}: "${option}"`);
                
                if (optionElement) {
                    optionElement.textContent = option;
                    log(`✅ 选项 ${optionKey} 文本已设置`);
                } else {
                    log(`❌ 找不到选项元素: option-${optionKey}-text`);
                }
                
                if (optionButton) {
                    // 确保选项按钮可见
                    optionButton.style.display = 'flex';
                    optionButton.style.visibility = 'visible';
                    log(`✅ 选项 ${optionKey} 按钮已显示`);
                } else {
                    log(`❌ 找不到选项按钮: [data-option="${optionKey}"]`);
                }
            });
            
            // 隐藏没有使用的选项
            const allOptionKeys = ['A', 'B', 'C', 'D'];
            allOptionKeys.slice(options.length).forEach(optionKey => {
                const optionButton = document.querySelector(`[data-option="${optionKey}"]`);
                if (optionButton) {
                    optionButton.style.display = 'none';
                    log(`🙈 隐藏未使用的选项: ${optionKey}`);
                }
            });
            
            resetRecognitionOptions();
            log('✅ Recognition题目显示完成');
        }
        
        function resetRecognitionOptions() {
            selectedRecognitionOptions = [];
            document.getElementById('recognition-confirm-btn').disabled = true;
            document.getElementById('recognition-feedback').style.display = 'none';
            
            // 重置所有选项的样式
            const options = ['A', 'B', 'C', 'D'];
            options.forEach(option => {
                const button = document.querySelector(`[data-option="${option}"]`);
                if (button) {
                    button.classList.remove('selected', 'correct', 'incorrect');
                }
            });
        }
        
        function selectRecognitionOption(option) {
            log('🎯 选择选项: ' + option);
            
            const currentButton = document.querySelector(`[data-option="${option}"]`);
            if (!currentButton) return;
            
            const isMultipleChoice = currentRecognitionData && currentRecognitionData.is_multiple_choice;
            
            if (isMultipleChoice) {
                // 多选模式：切换选择状态
                const index = selectedRecognitionOptions.indexOf(option);
                if (index > -1) {
                    // 取消选择
                    selectedRecognitionOptions.splice(index, 1);
                    currentButton.classList.remove('selected');
                    log('🔄 取消选择: ' + option);
                } else {
                    // 添加选择
                    selectedRecognitionOptions.push(option);
                    currentButton.classList.add('selected');
                    log('✅ 添加选择: ' + option);
                }
                
                log('🎯 多选当前选择: [' + selectedRecognitionOptions.join(', ') + ']');
            } else {
                // 单选模式
                selectedRecognitionOptions.forEach(prevOption => {
                    const prevButton = document.querySelector(`[data-option="${prevOption}"]`);
                    if (prevButton) {
                        prevButton.classList.remove('selected');
                    }
                });
                
                selectedRecognitionOptions = [option];
                currentButton.classList.add('selected');
                log('🎯 单选当前选择: ' + option);
            }
            
            // 启用确认按钮
            document.getElementById('recognition-confirm-btn').disabled = selectedRecognitionOptions.length === 0;
        }
        
        function confirmTest() {
            log('✅ 确认答案: [' + selectedRecognitionOptions.join(', ') + ']');
            log('📊 正确答案: [' + currentRecognitionData.correct_answers.join(', ') + ']');
            
            const resultElement = document.getElementById('recognition-result');
            const explanationElement = document.getElementById('recognition-explanation');
            
            resultElement.textContent = '测试完成！检查控制台日志。';
            explanationElement.textContent = '选择: [' + selectedRecognitionOptions.join(', ') + '], 正确答案: [' + currentRecognitionData.correct_answers.join(', ') + ']';
            
            document.getElementById('recognition-feedback').style.display = 'block';
        }
    </script>
</body>
</html>