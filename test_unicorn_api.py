#!/usr/bin/env python3
"""
测试独角兽图标API修复
"""
import requests
import json

def test_pattern_api():
    """测试pattern API是否正确返回is_linguistic_expert字段"""
    
    # 创建会话
    session = requests.Session()
    
    try:
        # 1. 登录
        print("1. 登录测试...")
        login_data = {'username': 'naonao', 'password': 'naonao'}
        login_response = session.post('http://localhost:5005/api/login', json=login_data)
        
        if login_response.status_code != 200:
            print(f"❌ 登录失败: {login_response.status_code}")
            print(f"登录响应: {login_response.text[:200]}")
            return False
        
        login_data = login_response.json()
        if not login_data.get('success'):
            print(f"❌ 登录API返回失败: {login_data.get('message', '未知错误')}")
            return False
        
        print("✅ 登录成功")
        
        # 2. 测试pattern API
        print("\n2. 测试Pattern API...")
        word_id = 32  # uncle这个有语言学标注的词
        api_url = f'http://localhost:5005/api/word_pattern_suggestions/{word_id}'
        
        response = session.get(api_url)
        
        if response.status_code != 200:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"响应内容: {response.text[:300]}")
            return False
        
        # 3. 检查响应数据
        data = response.json()
        
        if not data.get('success'):
            print(f"❌ API返回失败: {data.get('message', '未知错误')}")
            return False
        
        recommendations = data.get('data', {}).get('recommendations', [])
        
        if not recommendations:
            print("❌ 没有推荐数据")
            return False
        
        # 4. 检查关键字段
        print(f"✅ 获得 {len(recommendations)} 个推荐")
        
        for i, rec in enumerate(recommendations):
            pattern_info = rec.get('pattern_info', {})
            
            print(f"\n推荐 {i+1}:")
            print(f"  pattern_type: {pattern_info.get('pattern_type')}")
            print(f"  pattern_name: {pattern_info.get('pattern_name')}")
            print(f"  is_linguistic_expert: {pattern_info.get('is_linguistic_expert')}")
            
            if pattern_info.get('is_linguistic_expert'):
                print(f"  🦄 检测到语言学专家推荐!")
                print(f"  linguistic_principle: {pattern_info.get('linguistic_principle', 'N/A')}")
                return True
        
        print("❌ 未发现语言学专家推荐标志")
        return False
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False

if __name__ == "__main__":
    print("🦄 独角兽图标API修复测试")
    print("=" * 50)
    
    success = test_pattern_api()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 测试通过！独角兽图标API修复成功！")
        print("前端应该能正确显示🦄图标了")
    else:
        print("❌ 测试失败！需要进一步检查")